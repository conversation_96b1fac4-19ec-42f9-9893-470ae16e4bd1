package com.wosai.upay.job.biz.payBizApply;

import com.alibaba.fastjson.JSON;
import com.shouqianba.cua.enums.contract.ContractTaskTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.upay.job.biz.ContractParamsBiz;
import com.wosai.upay.job.constant.ContractRuleConstants;
import com.wosai.upay.job.constant.DevCodeConstants;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.DO.McAcquirerChange;
import com.wosai.upay.job.refactor.dao.MerchantProviderParamsDAO;
import com.wosai.upay.job.refactor.dao.PayBizApplyDAO;
import com.wosai.upay.job.refactor.dao.PayBizApplyLogDAO;
import com.wosai.upay.job.refactor.event.AcquirerChangeEvent;
import com.wosai.upay.job.refactor.event.ContractTaskEvent;
import com.wosai.upay.job.refactor.model.bo.PayBizApplyLogDetailBO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.refactor.model.entity.PayBizApplyDO;
import com.wosai.upay.job.refactor.model.enums.PayBizApplyOperationEnum;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.provider.TongLianV2Param;
import com.wosai.upay.merchant.contract.model.tlV2.request.ElectUrlQueryRequest;
import com.wosai.upay.merchant.contract.model.tlV2.response.ElectUrlQueryResponse;
import com.wosai.upay.merchant.contract.service.TongLianV2Service;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * TonglianV2PayBizApplyEventListener 单元测试
 */
@RunWith(MockitoJUnitRunner.class)
public class TonglianV2PayBizApplyEventListenerTest {

    @InjectMocks
    private TonglianV2PayBizApplyEventListener eventListener;

    @Mock
    private PayBizApplyDAO payBizApplyDAO;

    @Mock
    private PayBizApplyLogDAO payBizApplyLogDAO;

    @Mock
    private MerchantProviderParamsDAO merchantProviderParamsDAO;

    @Mock
    private TongLianV2Service tongLianV2Service;

    @Mock
    private ContractParamsBiz contractParamsBiz;

    private static final String TEST_MERCHANT_SN = "test_merchant_sn";
    private static final Long TEST_TASK_ID = 12345L;
    private static final long TEST_APPLY_ID = 67890L;
    private static final String TEST_FAIL_RESULT = "测试失败原因";
    private static final String TEST_SIGN_URL = "http://test.sign.url";

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    // ==================== ContractTaskEvent 测试 ====================

    @Test
    public void testHandleContractTaskEvent_Success() {
        // 准备测试数据
        ContractTask contractTask = createValidContractTask();
        ContractTaskEvent event = new ContractTaskEvent(contractTask);

        PayBizApplyDO mockApply = createMockPayBizApplyDO();
        when(payBizApplyDAO.selectByMerchantSnAndDevCode(TEST_MERCHANT_SN, DevCodeConstants.TONGLIAN_V2_DEV_CODE))
                .thenReturn(Optional.of(mockApply));

        // 执行测试
        ReflectionTestUtils.invokeMethod(eventListener, "handleContractTaskEventAsync", contractTask);

        // 验证调用
        verify(payBizApplyDAO).selectByMerchantSnAndDevCode(TEST_MERCHANT_SN, DevCodeConstants.TONGLIAN_V2_DEV_CODE);
    }

    @Test
    public void testHandleContractTaskEventAsync_WrongTaskType() {
        // 准备测试数据 - 错误的任务类型
        ContractTask contractTask = new ContractTask();
        contractTask.setMerchant_sn(TEST_MERCHANT_SN);
        contractTask.setId(TEST_TASK_ID);
        contractTask.setType(ContractTaskTypeEnum.UPDATE_MERCHANT_BASIC_INFO.getValue()); // 错误的类型
        contractTask.setRule_group_id(ContractRuleConstants.CHANGE_TO_TONGLIANV2_RULE_GROUP);
        contractTask.setStatus(TaskStatus.SUCCESS.getVal());

        // 执行测试
        ReflectionTestUtils.invokeMethod(eventListener, "handleContractTaskEventAsync", contractTask);

        // 验证没有调用DAO
        verify(payBizApplyDAO, never()).selectByMerchantSnAndDevCode(anyString(), anyString());
    }

    @Test
    public void testHandleContractTaskEventAsync_WrongRuleGroup() {
        // 准备测试数据 - 错误的规则组
        ContractTask contractTask = new ContractTask();
        contractTask.setMerchant_sn(TEST_MERCHANT_SN);
        contractTask.setId(TEST_TASK_ID);
        contractTask.setType(ContractTaskTypeEnum.NEW_MERCHANT_ONLINE.getValue());
        contractTask.setRule_group_id("wrong_rule_group"); // 错误的规则组
        contractTask.setStatus(TaskStatus.SUCCESS.getVal());

        // 执行测试
        ReflectionTestUtils.invokeMethod(eventListener, "handleContractTaskEventAsync", contractTask);

        // 验证没有调用DAO
        verify(payBizApplyDAO, never()).selectByMerchantSnAndDevCode(anyString(), anyString());
    }

    @Test
    public void testHandleContractTaskEventAsync_NoApplyFound() {
        // 准备测试数据
        ContractTask contractTask = createValidContractTask();
        when(payBizApplyDAO.selectByMerchantSnAndDevCode(TEST_MERCHANT_SN, DevCodeConstants.TONGLIAN_V2_DEV_CODE))
                .thenReturn(Optional.empty());

        // 执行测试
        ReflectionTestUtils.invokeMethod(eventListener, "handleContractTaskEventAsync", contractTask);

        // 验证调用
        verify(payBizApplyDAO).selectByMerchantSnAndDevCode(TEST_MERCHANT_SN, DevCodeConstants.TONGLIAN_V2_DEV_CODE);
        // 验证没有进一步处理
        verify(payBizApplyDAO, never()).completeCurrentStageAndMoveToNext(any());
    }

    @Test
    public void testHandleContractTaskEventAsync_ApplyNotProcessing() {
        // 准备测试数据
        ContractTask contractTask = createValidContractTask();
        PayBizApplyDO mockApply = createMockPayBizApplyDO();
        when(mockApply.isProcessing()).thenReturn(false); // 不是处理中状态

        when(payBizApplyDAO.selectByMerchantSnAndDevCode(TEST_MERCHANT_SN, DevCodeConstants.TONGLIAN_V2_DEV_CODE))
                .thenReturn(Optional.of(mockApply));

        // 执行测试
        ReflectionTestUtils.invokeMethod(eventListener, "handleContractTaskEventAsync", contractTask);

        // 验证没有进一步处理
        verify(payBizApplyDAO, never()).completeCurrentStageAndMoveToNext(any());
    }

    @Test
    public void testHandleContractTaskEventAsync_TaskIdMismatch() {
        // 准备测试数据
        ContractTask contractTask = createValidContractTask();
        PayBizApplyDO mockApply = createMockPayBizApplyDO();
        when(mockApply.getTaskId()).thenReturn(99999L); // 不匹配的taskId

        when(payBizApplyDAO.selectByMerchantSnAndDevCode(TEST_MERCHANT_SN, DevCodeConstants.TONGLIAN_V2_DEV_CODE))
                .thenReturn(Optional.of(mockApply));

        // 执行测试
        ReflectionTestUtils.invokeMethod(eventListener, "handleContractTaskEventAsync", contractTask);

        // 验证没有进一步处理
        verify(payBizApplyDAO, never()).completeCurrentStageAndMoveToNext(any());
    }

    @Test
    public void testHandleContractTaskEventAsync_TaskSuccess() throws Exception {
        // 准备测试数据
        ContractTask contractTask = createValidContractTask();
        contractTask.setStatus(TaskStatus.SUCCESS.getVal());

        PayBizApplyDO mockApply = createMockPayBizApplyDO();
        when(payBizApplyDAO.selectByMerchantSnAndDevCode(TEST_MERCHANT_SN, DevCodeConstants.TONGLIAN_V2_DEV_CODE))
                .thenReturn(Optional.of(mockApply));

        // Mock 签约链接查询
        mockSignUrlQuery(mockApply);

        // 执行测试
        ReflectionTestUtils.invokeMethod(eventListener, "handleContractTaskEventAsync", contractTask);

        // 验证处理成功状态的调用
        verify(payBizApplyDAO).completeCurrentStageAndMoveToNext(mockApply);
        verify(payBizApplyLogDAO).logOperationWithJsonDetail(
                eq(String.valueOf(TEST_APPLY_ID)),
                eq(PayBizApplyOperationEnum.ENTRY_AUDIT_PASS),
                eq("SYSTEM"),
                eq("SYSTEM"),
                eq("SYSTEM"),
                any(PayBizApplyLogDetailBO.class)
        );
    }

    @Test
    public void testHandleContractTaskEventAsync_TaskFailed() {
        // 准备测试数据
        ContractTask contractTask = createValidContractTask();
        contractTask.setStatus(TaskStatus.FAIL.getVal());
        contractTask.setResult(TEST_FAIL_RESULT);

        PayBizApplyDO mockApply = createMockPayBizApplyDO();
        when(payBizApplyDAO.selectByMerchantSnAndDevCode(TEST_MERCHANT_SN, DevCodeConstants.TONGLIAN_V2_DEV_CODE))
                .thenReturn(Optional.of(mockApply));

        // 执行测试
        ReflectionTestUtils.invokeMethod(eventListener, "handleContractTaskEventAsync", contractTask);

        // 验证处理失败状态的调用
        verify(payBizApplyDAO).updateToContractFailed(mockApply, TEST_FAIL_RESULT);
        verify(payBizApplyLogDAO).logOperationWithJsonDetail(
                eq(String.valueOf(TEST_APPLY_ID)),
                eq(PayBizApplyOperationEnum.ENTRY_AUDIT_FAIL),
                eq("SYSTEM"),
                eq("SYSTEM"),
                eq("SYSTEM"),
                any(PayBizApplyLogDetailBO.class)
        );
    }

    // ==================== AcquirerChangeEvent 测试 ====================

    @Test
    public void testHandleAcquirerChangeEvent_Success() {
        // 准备测试数据
        McAcquirerChange mcAcquirerChange = new McAcquirerChange();
        mcAcquirerChange.setApply_id(String.valueOf(TEST_APPLY_ID));
        AcquirerChangeEvent event = new AcquirerChangeEvent(mcAcquirerChange, TEST_MERCHANT_SN, true, null);

        PayBizApplyDO mockApply = createMockPayBizApplyDO();
        when(mockApply.isDetailEnabling()).thenReturn(true);
        when(mockApply.getChangeAcquirerApplyId()).thenReturn(String.valueOf(TEST_APPLY_ID));

        when(payBizApplyDAO.selectByMerchantSnAndDevCode(TEST_MERCHANT_SN, DevCodeConstants.TONGLIAN_V2_DEV_CODE))
                .thenReturn(Optional.of(mockApply));

        // 执行测试
        ReflectionTestUtils.invokeMethod(eventListener, "handleAcquirerChangeEventAsync", event);

        // 验证调用
        verify(payBizApplyDAO).selectByMerchantSnAndDevCode(TEST_MERCHANT_SN, DevCodeConstants.TONGLIAN_V2_DEV_CODE);
    }

    @Test
    public void testHandleAcquirerChangeEventAsync_NoApplyFound() {
        // 准备测试数据
        McAcquirerChange mcAcquirerChange = new McAcquirerChange();
        mcAcquirerChange.setApply_id(String.valueOf(TEST_APPLY_ID));
        AcquirerChangeEvent event = new AcquirerChangeEvent(mcAcquirerChange, TEST_MERCHANT_SN, true, null);

        when(payBizApplyDAO.selectByMerchantSnAndDevCode(TEST_MERCHANT_SN, DevCodeConstants.TONGLIAN_V2_DEV_CODE))
                .thenReturn(Optional.empty());

        // 执行测试
        ReflectionTestUtils.invokeMethod(eventListener, "handleAcquirerChangeEventAsync", event);

        // 验证没有进一步处理
        verify(payBizApplyDAO, never()).markApplyAsSuccess(any());
    }

    @Test
    public void testHandleAcquirerChangeEventAsync_ApplyIdMismatch() {
        // 准备测试数据
        McAcquirerChange mcAcquirerChange = new McAcquirerChange();
        mcAcquirerChange.setApply_id("99999"); // 不匹配的applyId
        AcquirerChangeEvent event = new AcquirerChangeEvent(mcAcquirerChange, TEST_MERCHANT_SN, true, null);

        PayBizApplyDO mockApply = createMockPayBizApplyDO();
        when(mockApply.isDetailEnabling()).thenReturn(true);
        when(mockApply.getChangeAcquirerApplyId()).thenReturn(String.valueOf(TEST_APPLY_ID));

        when(payBizApplyDAO.selectByMerchantSnAndDevCode(TEST_MERCHANT_SN, DevCodeConstants.TONGLIAN_V2_DEV_CODE))
                .thenReturn(Optional.of(mockApply));

        // 执行测试
        ReflectionTestUtils.invokeMethod(eventListener, "handleAcquirerChangeEventAsync", event);

        // 验证没有进一步处理
        verify(payBizApplyDAO, never()).markApplyAsSuccess(any());
    }

    @Test
    public void testHandleAcquirerChangeEventAsync_ChangeSuccess() {
        // 准备测试数据
        McAcquirerChange mcAcquirerChange = new McAcquirerChange();
        mcAcquirerChange.setApply_id(String.valueOf(TEST_APPLY_ID));
        AcquirerChangeEvent event = new AcquirerChangeEvent(mcAcquirerChange, TEST_MERCHANT_SN, true, null);

        PayBizApplyDO mockApply = createMockPayBizApplyDO();
        when(mockApply.isDetailEnabling()).thenReturn(true);
        when(mockApply.getChangeAcquirerApplyId()).thenReturn(String.valueOf(TEST_APPLY_ID));

        when(payBizApplyDAO.selectByMerchantSnAndDevCode(TEST_MERCHANT_SN, DevCodeConstants.TONGLIAN_V2_DEV_CODE))
                .thenReturn(Optional.of(mockApply));

        // 执行测试
        ReflectionTestUtils.invokeMethod(eventListener, "handleAcquirerChangeEventAsync", event);

        // 验证处理成功的调用
        verify(payBizApplyDAO).markApplyAsSuccess(mockApply);
        verify(payBizApplyLogDAO).logOperationWithJsonDetail(
                eq(String.valueOf(TEST_APPLY_ID)),
                eq(PayBizApplyOperationEnum.OPEN_SUCCESS),
                eq("SYSTEM"),
                eq("SYSTEM"),
                eq("SYSTEM"),
                any(PayBizApplyLogDetailBO.class)
        );
    }

    @Test
    public void testHandleAcquirerChangeEventAsync_ChangeFailed() {
        // 准备测试数据
        String failMsg = "切换失败原因";
        McAcquirerChange mcAcquirerChange = new McAcquirerChange();
        mcAcquirerChange.setApply_id(String.valueOf(TEST_APPLY_ID));
        AcquirerChangeEvent event = new AcquirerChangeEvent(mcAcquirerChange, TEST_MERCHANT_SN, false, failMsg);

        PayBizApplyDO mockApply = createMockPayBizApplyDO();
        when(mockApply.isDetailEnabling()).thenReturn(true);
        when(mockApply.getChangeAcquirerApplyId()).thenReturn(String.valueOf(TEST_APPLY_ID));

        when(payBizApplyDAO.selectByMerchantSnAndDevCode(TEST_MERCHANT_SN, DevCodeConstants.TONGLIAN_V2_DEV_CODE))
                .thenReturn(Optional.of(mockApply));

        // 执行测试
        ReflectionTestUtils.invokeMethod(eventListener, "handleAcquirerChangeEventAsync", event);

        // 验证处理失败的调用
        verify(payBizApplyDAO).updateToEnabledFailed(mockApply, failMsg);
        verify(payBizApplyLogDAO).logOperationWithJsonDetail(
                eq(String.valueOf(TEST_APPLY_ID)),
                eq(PayBizApplyOperationEnum.ENABLED_FAILED),
                eq("SYSTEM"),
                eq("SYSTEM"),
                eq("SYSTEM"),
                any(PayBizApplyLogDetailBO.class)
        );
    }

    // ==================== 签约链接保存测试 ====================

    @Test
    public void testSaveLegalSignUrl_Success() throws Exception {
        // 准备测试数据
        PayBizApplyDO mockApply = createMockPayBizApplyDO();
        mockSignUrlQuery(mockApply);

        // 执行测试
        ReflectionTestUtils.invokeMethod(eventListener, "saveLegalSignUrl", mockApply);

        // 验证调用
        verify(merchantProviderParamsDAO).getMerchantProviderParamsByProviderAndPayway(
                TEST_MERCHANT_SN, ProviderEnum.PROVIDER_TONGLIAN_V2.getValue(), PaywayEnum.ACQUIRER.getValue());
        verify(contractParamsBiz).buildContractParams(anyString(), anyInt(), anyString(), eq(TongLianV2Param.class));
        verify(tongLianV2Service).queryElectUrlV2(any(ElectUrlQueryRequest.class), any(TongLianV2Param.class));
        verify(payBizApplyDAO).saveLegalSignUrl(mockApply, TEST_SIGN_URL);
    }

    @Test
    public void testSaveLegalSignUrl_NoMerchantParams() throws Exception {
        // 准备测试数据
        PayBizApplyDO mockApply = createMockPayBizApplyDO();
        when(merchantProviderParamsDAO.getMerchantProviderParamsByProviderAndPayway(
                TEST_MERCHANT_SN, ProviderEnum.PROVIDER_TONGLIAN_V2.getValue(), PaywayEnum.ACQUIRER.getValue()))
                .thenReturn(Optional.empty());

        // 执行测试
        ReflectionTestUtils.invokeMethod(eventListener, "saveLegalSignUrl", mockApply);

        // 验证没有进一步调用
        verify(contractParamsBiz, never()).buildContractParams(anyString(), anyInt(), anyString(), any());
        verify(tongLianV2Service, never()).queryElectUrlV2(any(), any());
        verify(payBizApplyDAO, never()).saveLegalSignUrl(any(), anyString());
    }

    @Test
    public void testSaveLegalSignUrl_QueryFailed() throws Exception {
        // 准备测试数据
        PayBizApplyDO mockApply = createMockPayBizApplyDO();

        MerchantProviderParamsDO merchantParams = new MerchantProviderParamsDO();
        merchantParams.setProvider(ProviderEnum.PROVIDER_TONGLIAN_V2.getValue());
        merchantParams.setPayway(PaywayEnum.ACQUIRER.getValue());
        merchantParams.setChannelNo("test_channel");
        merchantParams.setPayMerchantId("test_pay_merchant_id");

        when(merchantProviderParamsDAO.getMerchantProviderParamsByProviderAndPayway(
                TEST_MERCHANT_SN, ProviderEnum.PROVIDER_TONGLIAN_V2.getValue(), PaywayEnum.ACQUIRER.getValue()))
                .thenReturn(Optional.of(merchantParams));

        TongLianV2Param tongLianV2Param = new TongLianV2Param();
        when(contractParamsBiz.buildContractParams(anyString(), anyInt(), anyString(), eq(TongLianV2Param.class)))
                .thenReturn(tongLianV2Param);

        ContractResponse failedResponse = new ContractResponse();
        failedResponse.setCode(500); // 设置为系统失败状态
        failedResponse.setMessage("查询失败");
        when(tongLianV2Service.queryElectUrlV2(any(ElectUrlQueryRequest.class), any(TongLianV2Param.class)))
                .thenReturn(failedResponse);

        // 执行测试
        ReflectionTestUtils.invokeMethod(eventListener, "saveLegalSignUrl", mockApply);

        // 验证没有保存签约链接
        verify(payBizApplyDAO, never()).saveLegalSignUrl(any(), anyString());
    }

    @Test
    public void testSaveLegalSignUrl_EmptyUrl() throws Exception {
        // 准备测试数据
        PayBizApplyDO mockApply = createMockPayBizApplyDO();

        MerchantProviderParamsDO merchantParams = new MerchantProviderParamsDO();
        merchantParams.setProvider(ProviderEnum.PROVIDER_TONGLIAN_V2.getValue());
        merchantParams.setPayway(PaywayEnum.ACQUIRER.getValue());
        merchantParams.setChannelNo("test_channel");
        merchantParams.setPayMerchantId("test_pay_merchant_id");

        when(merchantProviderParamsDAO.getMerchantProviderParamsByProviderAndPayway(
                TEST_MERCHANT_SN, ProviderEnum.PROVIDER_TONGLIAN_V2.getValue(), PaywayEnum.ACQUIRER.getValue()))
                .thenReturn(Optional.of(merchantParams));

        TongLianV2Param tongLianV2Param = new TongLianV2Param();
        when(contractParamsBiz.buildContractParams(anyString(), anyInt(), anyString(), eq(TongLianV2Param.class)))
                .thenReturn(tongLianV2Param);

        ContractResponse successResponse = new ContractResponse();
        successResponse.setCode(200); // 设置为成功状态
        ElectUrlQueryResponse queryResponse = new ElectUrlQueryResponse();
        queryResponse.setSybsignurl(""); // 空的签约链接
        successResponse.setResponseParam(JSON.parseObject(JSON.toJSONString(queryResponse), Map.class));
        when(tongLianV2Service.queryElectUrlV2(any(ElectUrlQueryRequest.class), any(TongLianV2Param.class)))
                .thenReturn(successResponse);

        // 执行测试
        ReflectionTestUtils.invokeMethod(eventListener, "saveLegalSignUrl", mockApply);

        // 验证没有保存签约链接
        verify(payBizApplyDAO, never()).saveLegalSignUrl(any(), anyString());
    }

    // ==================== 异常处理测试 ====================

    @Test
    public void testSaveLegalSignUrl_WithException() throws Exception {
        // 准备测试数据
        PayBizApplyDO mockApply = createMockPayBizApplyDO();

        // Mock 抛出异常
        when(merchantProviderParamsDAO.getMerchantProviderParamsByProviderAndPayway(anyString(), anyInt(), anyInt()))
                .thenThrow(new RuntimeException("数据库异常"));

        // 执行测试 - 不应该抛出异常
        ReflectionTestUtils.invokeMethod(eventListener, "saveLegalSignUrl", mockApply);

        // 验证调用了DAO
        verify(merchantProviderParamsDAO).getMerchantProviderParamsByProviderAndPayway(
                TEST_MERCHANT_SN, ProviderEnum.PROVIDER_TONGLIAN_V2.getValue(), PaywayEnum.ACQUIRER.getValue());
    }

    // ==================== 辅助方法 ====================

    /**
     * 创建有效的ContractTask
     */
    private ContractTask createValidContractTask() {
        ContractTask contractTask = new ContractTask();
        contractTask.setMerchant_sn(TEST_MERCHANT_SN);
        contractTask.setId(TEST_TASK_ID);
        contractTask.setType(ContractTaskTypeEnum.NEW_MERCHANT_ONLINE.getValue());
        contractTask.setRule_group_id(ContractRuleConstants.CHANGE_TO_TONGLIANV2_RULE_GROUP);
        contractTask.setStatus(TaskStatus.SUCCESS.getVal());
        return contractTask;
    }

    /**
     * 创建Mock的PayBizApplyDO
     */
    private PayBizApplyDO createMockPayBizApplyDO() {
        PayBizApplyDO mockApply = mock(PayBizApplyDO.class);
        when(mockApply.getId()).thenReturn(TEST_APPLY_ID);
        when(mockApply.getMerchantSn()).thenReturn(TEST_MERCHANT_SN);
        when(mockApply.getTaskId()).thenReturn(TEST_TASK_ID);
        when(mockApply.isProcessing()).thenReturn(true);
        when(mockApply.isDetailContractAuditing()).thenReturn(true);
        when(mockApply.getChangeAcquirerApplyId()).thenReturn(String.valueOf(TEST_APPLY_ID));
        return mockApply;
    }

    /**
     * Mock签约链接查询
     */
    private void mockSignUrlQuery(PayBizApplyDO mockApply) throws Exception {
        MerchantProviderParamsDO merchantParams = new MerchantProviderParamsDO();
        merchantParams.setProvider(ProviderEnum.PROVIDER_TONGLIAN_V2.getValue());
        merchantParams.setPayway(PaywayEnum.ACQUIRER.getValue());
        merchantParams.setChannelNo("test_channel");
        merchantParams.setPayMerchantId("test_pay_merchant_id");

        when(merchantProviderParamsDAO.getMerchantProviderParamsByProviderAndPayway(
                TEST_MERCHANT_SN, ProviderEnum.PROVIDER_TONGLIAN_V2.getValue(), PaywayEnum.ACQUIRER.getValue()))
                .thenReturn(Optional.of(merchantParams));

        TongLianV2Param tongLianV2Param = new TongLianV2Param();
        when(contractParamsBiz.buildContractParams(anyString(), anyInt(), anyString(), eq(TongLianV2Param.class)))
                .thenReturn(tongLianV2Param);

        ContractResponse successResponse = new ContractResponse();
        successResponse.setCode(200);
        ElectUrlQueryResponse queryResponse = new ElectUrlQueryResponse();
        queryResponse.setSybsignurl(TEST_SIGN_URL);
        successResponse.setResponseParam(JSON.parseObject(JSON.toJSONString(queryResponse), Map.class));
        when(tongLianV2Service.queryElectUrlV2(any(ElectUrlQueryRequest.class), any(TongLianV2Param.class)))
                .thenReturn(successResponse);
    }
}