package com.wosai.upay.job.xxljob.context;

import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.refactor.model.entity.PayBizApplyDO;
import com.wosai.upay.merchant.contract.model.provider.TongLianV2Param;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/9/15
 */
@Data
public class TonglianV2Context {

    private PayBizApplyDO apply;
    private MerchantProviderParamsDO merchantProviderParam;
    private TongLianV2Param tongLianV2Param;
    private boolean validated = false;

    private TonglianV2Context() {

    }

    public static TonglianV2Context create(PayBizApplyDO apply) {
        TonglianV2Context tonglianV2Context = new TonglianV2Context();
        tonglianV2Context.setApply(apply);
        return tonglianV2Context;
    }
}
