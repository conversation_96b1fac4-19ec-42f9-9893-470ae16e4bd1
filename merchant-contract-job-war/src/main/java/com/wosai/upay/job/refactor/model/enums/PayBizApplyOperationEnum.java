package com.wosai.upay.job.refactor.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 支付业务申请操作类型枚举
 *
 * <AUTHOR>
@Getter
@AllArgsConstructor
public enum PayBizApplyOperationEnum {

    /**
     * 创建申请单
     */
    CREATE_APPLY("CREATE_APPLY", "创建申请单"),

    /**
     * 重新提交申请
     */
    RESUBMIT_APPLY("RESUBMIT_APPLY", "重新提交申请"),

    /**
     * 进件审核通过
     */
    ENTRY_AUDIT_PASS("ENTRY_AUDIT_PASS", "进件审核通过"),

    /**
     * 进件审核失败
     */
    ENTRY_AUDIT_FAIL("ENTRY_AUDIT_FAIL", "进件审核失败"),

    /**
     * 法人签约成功
     */
    LEGAL_SIGN_SUCCESS("LEGAL_SIGN_SUCCESS", "法人签约成功"),

    /**
     * 法人签约失败
     */
    LEGAL_SIGN_FAIL("LEGAL_SIGN_FAIL", "法人签约失败"),

    /**
     * 结算人签约成功
     */
    SETTLEMENT_SIGN_SUCCESS("SETTLEMENT_SIGN_SUCCESS", "结算人签约成功"),

    /**
     * 结算人签约失败
     */
    SETTLEMENT_SIGN_FAIL("SETTLEMENT_SIGN_FAIL", "结算人签约失败"),

    /**
     * 合规补录审核通过
     */
    COMPLIANCE_AUDIT_PASS("COMPLIANCE_AUDIT_PASS", "合规补录审核通过"),

    /**
     * 合规补录审核失败
     */
    COMPLIANCE_AUDIT_FAIL("COMPLIANCE_AUDIT_FAIL", "合规补录审核失败"),

    /**
     * 合规性补录成功
     */
    COMPLIANCE_REPAIR_SUCCESS("COMPLIANCE_REPAIR_SUCCESS", "合规性补录成功"),

    /**
     * 合规性补录失败
     */
    COMPLIANCE_REPAIR_FAILED("COMPLIANCE_REPAIR_FAILED", "合规性补录失败"),

    /**
     * 启用失败
     */
    ENABLED_FAILED("ENABLED_FAILED", "启用失败"),

    /**
     * 开通成功
     */
    OPEN_SUCCESS("OPEN_SUCCESS", "开通成功"),

    /**
     * 开通失败
     */
    OPEN_FAIL("OPEN_FAIL", "开通失败"),

    /**
     * 状态回退
     */
    STATUS_ROLLBACK("STATUS_ROLLBACK", "状态回退"),

    /**
     * 手动干预
     */
    MANUAL_INTERVENTION("MANUAL_INTERVENTION", "手动干预"),

    /**
     * 系统自动处理
     */
    SYSTEM_AUTO_PROCESS("SYSTEM_AUTO_PROCESS", "系统自动处理"),
    /**
     * 重新发送签约链接
     */
    RESIGN("RESEND_SIGN", "重新发送签约链接"),

    /**
     * 签约链接失效
     */
    SIGN_URL_EXPIRED("SIGN_URL_EXPIRED", "签约链接失效"),

    /**
     * 签约成功
     */
    SIGN_SUCCESS("SIGN_SUCCESS", "签约成功"),

    /**
     * 签约失败
     */
    SIGN_FAILED("SIGN_FAILED", "签约失败");

    /**
     * 操作类型（英文）
     */
    private final String operationType;

    /**
     * 操作描述（中文）
     */
    private final String desc;
}
