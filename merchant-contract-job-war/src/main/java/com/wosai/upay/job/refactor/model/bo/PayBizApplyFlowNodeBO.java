package com.wosai.upay.job.refactor.model.bo;

import com.alibaba.fastjson.annotation.JSONField;
import com.wosai.upay.job.refactor.model.enums.PayBizApplyFlowNodeStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 支付业务申请流程节点BO
 *
 * <AUTHOR>
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PayBizApplyFlowNodeBO {

    /**
     * 详细状态码，对应PayBizApplyDetailStatusEnum
     */
    private Integer detailStatus;

    /**
     * 节点描述文本
     */
    private String text;

    /**
     * 节点状态：0-未处理，1-进行中，2-成功
     */
    private Integer status;

    /**
     * 完成时间
     */
    private LocalDateTime time;

    /**
     * 创建未处理的流程节点
     *
     * @param detailStatus 详细状态码
     * @param text 节点描述文本
     * @return 未处理的流程节点
     */
    public static PayBizApplyFlowNodeBO createUnprocessedNode(Integer detailStatus, String text) {
        return new PayBizApplyFlowNodeBO(detailStatus, text, PayBizApplyFlowNodeStatusEnum.UNPROCESSED.getCode(), null);
    }

    /**
     * 创建进行中的流程节点
     *
     * @param detailStatus 详细状态码
     * @param text 节点描述文本
     * @return 进行中的流程节点
     */
    public static PayBizApplyFlowNodeBO createInProgressNode(Integer detailStatus, String text) {
        return new PayBizApplyFlowNodeBO(detailStatus, text, PayBizApplyFlowNodeStatusEnum.IN_PROGRESS.getCode(), null);
    }

    /**
     * 创建成功的流程节点
     *
     * @param detailStatus 详细状态码
     * @param text 节点描述文本
     * @param finishTime 完成时间
     * @return 成功的流程节点
     */
    public static PayBizApplyFlowNodeBO createSuccessNode(Integer detailStatus, String text, LocalDateTime finishTime) {
        return new PayBizApplyFlowNodeBO(detailStatus, text, PayBizApplyFlowNodeStatusEnum.SUCCESS.getCode(), finishTime);
    }

    /**
     * 标记节点为成功状态
     *
     * @param finishTime 完成时间
     */
    public void markAsSuccess(LocalDateTime finishTime) {
        this.status = PayBizApplyFlowNodeStatusEnum.SUCCESS.getCode();
        this.time = finishTime;
    }

    /**
     * 标记节点为成功状态（使用当前时间）
     */
    public void markAsSuccess() {
        markAsSuccess(LocalDateTime.now());
    }

    /**
     * 标记节点为进行中状态
     */
    public void markAsInProgress() {
        this.status = PayBizApplyFlowNodeStatusEnum.IN_PROGRESS.getCode();
        this.time = null; // 进行中状态清空完成时间
    }

    /**
     * 标记节点为未处理状态
     */
    public void markAsUnprocessed() {
        this.status = PayBizApplyFlowNodeStatusEnum.UNPROCESSED.getCode();
        this.time = null; // 未处理状态清空完成时间
    }

    /**
     * 判断节点是否为未处理状态
     *
     * @return true-未处理，false-非未处理
     */
    @JSONField(serialize = false, deserialize = false)
    public boolean isUnprocessed() {
        return PayBizApplyFlowNodeStatusEnum.isUnprocessed(this.status);
    }

    /**
     * 判断节点是否为进行中状态
     *
     * @return true-进行中，false-非进行中
     */
    @JSONField(serialize = false, deserialize = false)
    public boolean isInProgress() {
        return PayBizApplyFlowNodeStatusEnum.isInProgress(this.status);
    }

    /**
     * 判断节点是否为成功状态
     *
     * @return true-成功，false-非成功
     */
    @JSONField(serialize = false, deserialize = false)
    public boolean isSuccess() {
        return PayBizApplyFlowNodeStatusEnum.isSuccess(this.status);
    }

    /**
     * 判断节点是否已完成（成功）
     *
     * @return true-已完成，false-未完成
     */
    @JSONField(serialize = false, deserialize = false)
    public boolean isFinished() {
        return PayBizApplyFlowNodeStatusEnum.isFinished(this.status);
    }

    /**
     * 判断节点是否未完成（未处理或进行中）
     *
     * @return true-未完成，false-已完成
     */
    @JSONField(serialize = false)
    public boolean isUnfinished() {
        return PayBizApplyFlowNodeStatusEnum.isUnfinished(this.status);
    }

    /**
     * 获取状态枚举
     *
     * @return 状态枚举
     */
    @JSONField(serialize = false, deserialize = false)
    public PayBizApplyFlowNodeStatusEnum getStatusEnum() {
        return PayBizApplyFlowNodeStatusEnum.getByCode(this.status);
    }
}
