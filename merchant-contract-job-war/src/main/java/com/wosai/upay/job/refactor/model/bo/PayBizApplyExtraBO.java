package com.wosai.upay.job.refactor.model.bo;

import com.alibaba.fastjson.JSON;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/9/13
 */
@Data
@NoArgsConstructor
public class PayBizApplyExtraBO {

    private Long taskId;
    private String changeAcquirerApplyId;
    private String legalSignUrl;
    private Long legalSignUrlTime;
    private String settlementSignUrl;
    private Long settlementSignUrlTime;
    private String handleDetail;

    public static PayBizApplyExtraBO fromJsonString(String json) {
        return JSON.parseObject(json, PayBizApplyExtraBO.class);
    }

    public String toJsonString() {
        return JSON.toJSONString(this);
    }

    public static PayBizApplyExtraBO createEmpty() {
        return new PayBizApplyExtraBO();
    }

    public static PayBizApplyExtraBO createWithTaskId(Long taskId) {
        PayBizApplyExtraBO extra = new PayBizApplyExtraBO();
        extra.setTaskId(taskId);
        return extra;
    }
}
