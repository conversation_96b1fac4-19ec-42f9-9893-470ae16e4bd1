package com.wosai.upay.job.refactor.model.enums;

import com.wosai.upay.job.enume.tonglianV2.PayBizApplyDetailStatusEnum;
import com.wosai.upay.job.refactor.model.bo.PayBizApplyFlowInfoBO;
import com.wosai.upay.job.refactor.model.bo.PayBizApplyFlowNodeBO;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * 支付业务申请流程节点枚举
 *
 * <AUTHOR>
@Getter
@AllArgsConstructor
public enum PayBizApplyFlowNodeEnum {

    /**
     * 进件审核
     */
    ENTRY_AUDIT(PayBizApplyDetailStatusEnum.CONTRACT_AUDITING.getCode(), "进件审核", 1),

    /**
     * 法人签约
     */
    LEGAL_SIGN(PayBizApplyDetailStatusEnum.WAITING_LEGAL_SIGN.getCode(), "法人签约", 2),

    /**
     * 待结算人签约（可选节点）
     */
    SETTLEMENT_SIGN(PayBizApplyDetailStatusEnum.WAITING_SETTLEMENT_SIGN.getCode(), "待结算人签约", 3),

    /**
     * 合规补录审核
     */
    COMPLIANCE_AUDIT(PayBizApplyDetailStatusEnum.COMPLIANCE_SUPPLEMENT_AUDITING.getCode(), "合规补录审核", 4),

    /**
     * 待生效
     */
    WAITING_EFFECTIVE(PayBizApplyDetailStatusEnum.WAITING_EFFECTIVE.getCode(), "待生效", 5),

    /**
     * 开通成功
     */
    OPEN_SUCCESS(PayBizApplyDetailStatusEnum.OPEN_SUCCESS.getCode(), "开通成功", 6);

    /**
     * 详细状态码
     */
    private final Integer detailStatus;

    /**
     * 节点名称
     */
    private final String nodeName;

    /**
     * 节点顺序
     */
    private final Integer order;

    /**
     * 根据详细状态码获取枚举
     *
     * @param detailStatus 详细状态码
     * @return 对应的枚举，如果不存在则返回null
     */
    public static PayBizApplyFlowNodeEnum getByDetailStatus(Integer detailStatus) {
        if (detailStatus == null) {
            return null;
        }
        for (PayBizApplyFlowNodeEnum node : values()) {
            if (node.getDetailStatus().equals(detailStatus)) {
                return node;
            }
        }
        return null;
    }

    /**
     * 根据顺序获取枚举
     *
     * @param order 节点顺序
     * @return 对应的枚举，如果不存在则返回null
     */
    public static PayBizApplyFlowNodeEnum getByOrder(Integer order) {
        if (order == null) {
            return null;
        }
        for (PayBizApplyFlowNodeEnum node : values()) {
            if (node.getOrder().equals(order)) {
                return node;
            }
        }
        return null;
    }

    /**
     * 获取下一个节点
     *
     * @return 下一个节点，如果当前是最后一个节点则返回null
     */
    public PayBizApplyFlowNodeEnum getNextNode() {
        return getByOrder(this.order + 1);
    }

    /**
     * 获取上一个节点
     *
     * @return 上一个节点，如果当前是第一个节点则返回null
     */
    public PayBizApplyFlowNodeEnum getPreviousNode() {
        return getByOrder(this.order - 1);
    }

    /**
     * 判断是否为第一个节点
     *
     * @return true-第一个节点，false-非第一个节点
     */
    public boolean isFirstNode() {
        return this.order == 1;
    }

    /**
     * 判断是否为最后一个节点
     *
     * @return true-最后一个节点，false-非最后一个节点
     */
    public boolean isLastNode() {
        return getNextNode() == null;
    }

    /**
     * 创建流程节点BO（未处理状态）
     *
     * @return 流程节点BO
     */
    public PayBizApplyFlowNodeBO createFlowNodeBO() {
        return PayBizApplyFlowNodeBO.createUnprocessedNode(this.detailStatus, this.nodeName);
    }

    /**
     * 创建流程节点BO（指定状态）
     *
     * @param status 节点状态
     * @return 流程节点BO
     */
    public PayBizApplyFlowNodeBO createFlowNodeBO(Integer status) {
        return new PayBizApplyFlowNodeBO(this.detailStatus, this.nodeName, status, null);
    }

    /**
     * 创建标准的流程信息BO（包含所有节点，包括待结算人签约）
     *
     * @return 包含所有标准节点的流程信息BO
     */
    public static PayBizApplyFlowInfoBO createStandardFlowInfo() {
        List<PayBizApplyFlowNodeBO> nodes = new ArrayList<>();
        PayBizApplyFlowNodeEnum[] allNodes = values();
        for (int i = 0; i < allNodes.length; i++) {
            PayBizApplyFlowNodeEnum nodeEnum = allNodes[i];
            if (i == 0) {
                // 第一个节点设置为进行中
                nodes.add(nodeEnum.createFlowNodeBO(PayBizApplyFlowNodeStatusEnum.IN_PROGRESS.getCode()));
            } else {
                // 其他节点设置为未处理
                nodes.add(nodeEnum.createFlowNodeBO());
            }
        }
        return new PayBizApplyFlowInfoBO(nodes);
    }

    /**
     * 创建不包含待结算人签约的流程信息BO
     * 流程：进件审核 → 法人签约 → 合规补录审核 → 待生效 → 开通成功
     *
     * @return 不包含待结算人签约的流程信息BO
     */
    public static PayBizApplyFlowInfoBO createFlowInfoWithoutSettlementSign() {
        return createCustomFlowInfo(
            ENTRY_AUDIT,
            LEGAL_SIGN,
            COMPLIANCE_AUDIT,
            WAITING_EFFECTIVE,
            OPEN_SUCCESS
        );
    }

    /**
     * 创建包含待结算人签约的流程信息BO
     * 流程：进件审核 → 法人签约 → 待结算人签约 → 合规补录审核 → 待生效 → 开通成功
     *
     * @return 包含待结算人签约的流程信息BO
     */
    public static PayBizApplyFlowInfoBO createFlowInfoWithSettlementSign() {
        return createCustomFlowInfo(
            ENTRY_AUDIT,
            LEGAL_SIGN,
            SETTLEMENT_SIGN,
            COMPLIANCE_AUDIT,
            WAITING_EFFECTIVE,
            OPEN_SUCCESS
        );
    }

    /**
     * 创建自定义流程信息BO（第一个节点为进行中）
     *
     * @param nodeEnums 要包含的节点枚举列表
     * @return 自定义的流程信息BO
     */
    public static PayBizApplyFlowInfoBO createCustomFlowInfo(PayBizApplyFlowNodeEnum... nodeEnums) {
        List<PayBizApplyFlowNodeBO> nodes = new ArrayList<>();
        for (int i = 0; i < nodeEnums.length; i++) {
            PayBizApplyFlowNodeEnum nodeEnum = nodeEnums[i];
            if (i == 0) {
                // 第一个节点设置为进行中
                nodes.add(nodeEnum.createFlowNodeBO(PayBizApplyFlowNodeStatusEnum.IN_PROGRESS.getCode()));
            } else {
                // 其他节点设置为未处理
                nodes.add(nodeEnum.createFlowNodeBO());
            }
        }
        return new PayBizApplyFlowInfoBO(nodes);
    }

    /**
     * 获取所有节点名称
     *
     * @return 节点名称列表
     */
    public static List<String> getAllNodeNames() {
        List<String> names = new ArrayList<>();
        for (PayBizApplyFlowNodeEnum node : values()) {
            names.add(node.getNodeName());
        }
        return names;
    }

    /**
     * 获取节点总数
     *
     * @return 节点总数
     */
    public static int getTotalNodeCount() {
        return values().length;
    }
}
