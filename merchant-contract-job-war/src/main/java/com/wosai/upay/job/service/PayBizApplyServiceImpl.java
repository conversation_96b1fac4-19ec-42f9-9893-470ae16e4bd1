package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.upay.job.biz.payBizApply.PayBizApplyHandleService;
import com.wosai.upay.job.biz.payBizApply.PayBizApplyHandleServiceFactory;
import com.wosai.upay.job.enume.tonglianV2.PayBizApplyDetailStatusEnum;
import com.wosai.upay.job.model.dto.ProcessRecordInfoDTO;
import com.wosai.upay.job.model.dto.request.PayBizApplyReqDTO;
import com.wosai.upay.job.model.dto.request.PayBizApplyStatusReqDTO;
import com.wosai.upay.job.model.dto.request.PayBizEnabledSetReqDTO;
import com.wosai.upay.job.model.dto.request.PayBizSignResendReqDTO;
import com.wosai.upay.job.model.dto.request.PayBizSignUrlValidateReqDTO;
import com.wosai.upay.job.model.dto.request.ProcessRecordsGetReqDTO;
import com.wosai.upay.job.model.dto.response.CuaCommonResultDTO;
import com.wosai.upay.job.model.dto.response.PayBizApplyStatusDetailRspDTO;
import com.wosai.upay.job.model.dto.response.PayBizApplyStatusRspDTO;
import com.wosai.upay.job.refactor.dao.PayBizApplyDAO;
import com.wosai.upay.job.refactor.dao.PayBizApplyLogDAO;
import com.wosai.upay.job.refactor.model.bo.PayBizApplyLogDetailBO;
import com.wosai.upay.job.refactor.model.entity.PayBizApplyDO;
import com.wosai.upay.job.refactor.model.entity.PayBizApplyLogDO;
import com.wosai.upay.job.util.DateUtil;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 支付业务开通申请服务实现类
 *
 * <AUTHOR>
 * @date 2025/9/12
 */
@Slf4j
@Service
@AutoJsonRpcServiceImpl
public class PayBizApplyServiceImpl implements PayBizApplyService {

    @Autowired
    private PayBizApplyHandleServiceFactory handleServiceFactory;
    @Autowired
    private PayBizApplyDAO payBizApplyDAO;
    @Autowired
    private PayBizApplyLogDAO payBizApplyLogDAO;

    @Override
    public CuaCommonResultDTO applyPayBiz(PayBizApplyReqDTO request) {
        String devCode = request.getDevCode();
        // 根据devCode获取对应的处理服务
        PayBizApplyHandleService handleService = handleServiceFactory.getHandleService(devCode);
        if (handleService == null) {
            log.error("未找到对应的支付业务申请处理服务，devCode: {}", devCode);
            return CuaCommonResultDTO.fail("不支持的业务类型: " + devCode);
        }

        // 委托给具体的处理服务
        return handleService.handleApply(request);
    }

    @Override
    public PayBizApplyStatusDetailRspDTO getPayBizApplyStatusDetailInfo(PayBizApplyStatusReqDTO request) {
        String devCode = request.getDevCode();
        // 根据devCode获取对应的处理服务
        PayBizApplyHandleService handleService = handleServiceFactory.getHandleService(devCode);
        if (handleService == null) {
            log.error("未找到对应的支付业务申请处理服务，devCode: {}", devCode);
            throw new ContractBizException("不支持的业务类型: " + devCode);
        }
        // 委托给具体的处理服务 TODO 场景码对接
        return handleService.getPayBizApplyStatusDetailInfo(request);
    }

    @Override
    public PayBizApplyStatusRspDTO getPayBizApplyStatusInfo(PayBizApplyStatusReqDTO request) {
        Optional<PayBizApplyDO> payBizApplyDO = payBizApplyDAO.selectByMerchantSnAndDevCode(request.getMerchantSn(), request.getDevCode());
        if (payBizApplyDO.isPresent()) {
            PayBizApplyDO apply = payBizApplyDO.get();
            PayBizApplyStatusRspDTO response = new PayBizApplyStatusRspDTO();
            PayBizApplyDetailStatusEnum detailStatusEnum = apply.getDetailStatusEnum();
            response.setStatus(apply.getStatus());
            response.setDetailStatus(apply.getDetailStatus());
            response.setStatusDesc(apply.getStatusEnum().getDesc());
            response.setDetailStatusDesc(detailStatusEnum.getDesc());
            // TODO 场景码对接
            response.setDescription(detailStatusEnum.getDescription());
            if (apply.isFailed() || apply.isEnabledFailed()) {
                response.setDescription(apply.getResult());
            }
            response.setDisplayText(detailStatusEnum.getDisplayText());
            return response;
        }
        throw new ContractBizException("未找到申请记录");
    }

    @Override
    public List<ProcessRecordInfoDTO> getProcessRecords(ProcessRecordsGetReqDTO request) {
        Optional<PayBizApplyDO> payBizApplyDO = payBizApplyDAO.selectByMerchantSnAndDevCode(request.getMerchantSn(), request.getDevCode());
        if (!payBizApplyDO.isPresent()) {
            throw new ContractBizException("未找到申请记录");
        }
        List<PayBizApplyLogDO> payBizApplyLogDOS = payBizApplyLogDAO.selectByBizId(String.valueOf(payBizApplyDO.get().getId()));
        List<ProcessRecordInfoDTO> processRecordInfoDTOList = new ArrayList<>();
        for (PayBizApplyLogDO payBizApplyLogDO : payBizApplyLogDOS) {
            PayBizApplyLogDetailBO detailBO = PayBizApplyLogDetailBO.fromJsonString(payBizApplyLogDO.getDetail());
            if (Objects.nonNull(detailBO)) {
                ProcessRecordInfoDTO processRecordInfoDTO = new ProcessRecordInfoDTO();
                processRecordInfoDTO.setTime(Objects.nonNull(detailBO.getOperateTime()) ? detailBO.getOperateTime().format(DateUtil.DATE_TIME_FORMATTER) : payBizApplyLogDO.getCtime().format(DateUtil.DATE_TIME_FORMATTER));
                processRecordInfoDTO.setStatus(detailBO.getStatus());
                processRecordInfoDTO.setDetailStatus(detailBO.getDetailStatus());
                processRecordInfoDTO.setDisplayText(detailBO.getDisplayText());
                processRecordInfoDTO.setDescription(detailBO.getDescription());
                processRecordInfoDTOList.add(processRecordInfoDTO);
            }
        }
        return processRecordInfoDTOList;
    }

    @Override
    public CuaCommonResultDTO setPayBizEnabled(PayBizEnabledSetReqDTO request) {
        String devCode = request.getDevCode();
        // 根据devCode获取对应的处理服务
        PayBizApplyHandleService handleService = handleServiceFactory.getHandleService(devCode);
        if (handleService == null) {
            log.error("未找到对应的支付业务申请处理服务，devCode: {}", devCode);
            return CuaCommonResultDTO.fail("不支持的业务类型: " + devCode);
        }

        // 委托给具体的处理服务
        return handleService.setPayBizEnabled(request);
    }

    @Override
    public CuaCommonResultDTO reSendSignUrl(PayBizSignResendReqDTO request) {
        String devCode = request.getDevCode();
        // 根据devCode获取对应的处理服务
        PayBizApplyHandleService handleService = handleServiceFactory.getHandleService(devCode);
        if (handleService == null) {
            log.error("未找到对应的支付业务申请处理服务，devCode: {}", devCode);
            return CuaCommonResultDTO.fail("不支持的业务类型: " + devCode);
        }

        // 委托给具体的处理服务
        return handleService.reSendSignUrl(request);
    }

    @Override
    public CuaCommonResultDTO validateSignUrl(PayBizSignUrlValidateReqDTO request) {
        String devCode = request.getDevCode();
        // 根据devCode获取对应的处理服务
        PayBizApplyHandleService handleService = handleServiceFactory.getHandleService(devCode);
        if (handleService == null) {
            log.error("未找到对应的支付业务申请处理服务，devCode: {}", devCode);
            return CuaCommonResultDTO.fail("不支持的业务类型: " + devCode);
        }

        // 委托给具体的处理服务
        return handleService.validateSignUrl(request);
    }
}
