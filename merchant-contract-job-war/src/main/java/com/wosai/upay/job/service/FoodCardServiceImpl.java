package com.wosai.upay.job.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.BusinessLicenseTypeEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.shouqianba.cua.enums.status.ConfigStatusEnum;
import com.shouqianba.cua.enums.status.UpdateStatusEnum;
import com.wosai.data.dao.DaoConstants;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.upay.core.model.MerchantAppConfig;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.biz.ContractParamsBiz;
import com.wosai.upay.job.biz.SubBizParamsBiz;
import com.wosai.upay.job.enume.ChannelEnum;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.dto.request.FoodCardConfigTradeParamReqDTO;
import com.wosai.upay.job.model.dto.request.FoodCardOpenReqDTO;
import com.wosai.upay.job.model.dto.response.CuaCommonResultDTO;
import com.wosai.upay.job.model.subBizParams.SubBizConfig;
import com.wosai.upay.job.model.subBizParams.SubBizParams;
import com.wosai.upay.job.providers.HaikeProvider;
import com.wosai.upay.job.providers.LexinProvider;
import com.wosai.upay.job.refactor.biz.merchant.MerchantBasicInfoBiz;
import com.wosai.upay.job.refactor.biz.params.MerchantTradeParamsBiz;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.util.CommonUtil;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.provider.HaikeParam;
import com.wosai.upay.merchant.contract.service.HaikeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 饭卡支付
 *
 * <AUTHOR>
 * @date 2025/5/23 15:03
 */
@Component
@Slf4j
@AutoJsonRpcServiceImpl
public class FoodCardServiceImpl implements FoodCardService {

    @Autowired
    private HaikeService haikeService;

    @Resource
    private ContractParamsBiz contractParamsBiz;

    @Resource
    private MerchantTradeParamsBiz merchantTradeParamsBiz;

    @Resource
    private MerchantProviderParamsMapper merchantProviderParamsMapper;

    @Autowired
    private SubBizParamsBiz subBizParamsBiz;

    @Resource
    private MerchantBasicInfoBiz merchantBasicInfoBiz;

    public static final String LE_XIN_MCH_ID_KEY = "lexin_mch_id";

    public static final String MERCHANT_NAME = "merchant_name";

    public static final String PROVIDER_MCH_ID = "provider_mch_id";

    public static final String PROVIDER_MCH_SUB_ID = "provider_sub_mch_id";

    public static final String PROVIDER_AGREEMENT_NO = "provider_agreement_no";

    public static final String PARTNER_ID = "partner_id";

    @Resource
    private HaikeProvider haikeProvider;

    @Resource
    private LexinProvider lexinProvider;

    @Resource
    private ApplicationApolloConfig applicationApolloConfig;

    @Autowired
    private TradeConfigService tradeConfigService;

    public static final String DEFAULT_FEE_RATE = "0.38";

    public static final String DEFAULT_HAIKE_FOOD_CARD_RULE = "haike-food_card";
    
    public static final String DEFAULT_LEXIN_FOOD_CARD_RULE = "lexin-food_card";

    /**
     * 判断新旧营业执照类型是否属于小微升级
     *
     * @param newLicenseType 新营业执照类型
     * @param oldLicenseType 旧营业执照类型
     * @return true：小微升级，false：非小微升级
     */
    @Override
    public Boolean isLicenseMicroUpgrade(Integer newLicenseType, Integer oldLicenseType) {
        if (Objects.isNull(oldLicenseType) || Objects.equals(oldLicenseType, BusinessLicenseTypeEnum.MICRO.getValue())) {
            return Objects.nonNull(newLicenseType) && newLicenseType > BusinessLicenseTypeEnum.MICRO.getValue();
        }
        return false;
    }

    /**
     * 上传协议附件
     * 注意：收单机构接口响应时间不可控，建议rpc超时时间设置长一点
     *
     * @param foodCardOpenReqDTO 协议附件请求
     * @return 上传结果
     */
    @Override
    public CuaCommonResultDTO openHaikeFoodCard(FoodCardOpenReqDTO foodCardOpenReqDTO) {
        try {
            String merchantSn = foodCardOpenReqDTO.getMerchantSn();
            Optional<String> acquirerMerchantId = merchantTradeParamsBiz.getAcquirerMerchantId(merchantSn, ChannelEnum.HAIKE.getValue());
            if (!acquirerMerchantId.isPresent()) {
                return CuaCommonResultDTO.fail("海科尚未进件成功");
            }
            HaikeParam haikeParam = contractParamsBiz.buildContractParams(ChannelEnum.HAIKE.getValue(), HaikeParam.class);
            ContractResponse contractResponse = haikeService.openHaikeFoodCard(acquirerMerchantId.get(), foodCardOpenReqDTO.getFileName(), foodCardOpenReqDTO.getFileUrl(), haikeParam);
            if (contractResponse.isSuccess()) {
                return CuaCommonResultDTO.success();
            }
            return CuaCommonResultDTO.fail(contractResponse.getMessage());
        } catch (ContractBizException e) {
            log.error("openHaikeFoodCard error, merchantSn:{}", foodCardOpenReqDTO.getMerchantSn(), e);
            return CuaCommonResultDTO.fail(e.getMessage());
        } catch (Exception e) {
            log.error("openHaikeFoodCard error, merchantSn:{}", foodCardOpenReqDTO.getMerchantSn(), e);
            return CuaCommonResultDTO.fail("开通海科饭卡支付失败");
        }
    }

    /**
     * 更新海科饭卡支付
     * 涉及对接海科的附件上传和饭卡业务申请接口
     * 注意：收单机构接口响应时间不可控，建议rpc超时时间设置长一点
     *
     * @param foodCardOpenReqDTO 开通请求
     * @return 开通结果
     */
    @Override
    public CuaCommonResultDTO modifyHaikeFoodCard(FoodCardOpenReqDTO foodCardOpenReqDTO) {
        try {
            String merchantSn = foodCardOpenReqDTO.getMerchantSn();
            Optional<String> acquirerMerchantId = merchantTradeParamsBiz.getAcquirerMerchantId(merchantSn, ChannelEnum.HAIKE.getValue());
            if (!acquirerMerchantId.isPresent()) {
                return CuaCommonResultDTO.fail("海科尚未进件成功");
            }
            HaikeParam haikeParam = contractParamsBiz.buildContractParams(ChannelEnum.HAIKE.getValue(), HaikeParam.class);
            ContractResponse contractResponse = haikeService.modifyHaikeFoodCard(acquirerMerchantId.get(), foodCardOpenReqDTO.getFileName(), foodCardOpenReqDTO.getFileUrl(), haikeParam);
            if (contractResponse.isSuccess()) {
                return CuaCommonResultDTO.success();
            }
            return CuaCommonResultDTO.fail(contractResponse.getMessage());
        } catch (ContractBizException e) {
            log.error("updateHaikeFoodCard error, merchantSn:{}", foodCardOpenReqDTO.getMerchantSn(), e);
            return CuaCommonResultDTO.fail(e.getMessage());
        } catch (Exception e) {
            log.error("updateHaikeFoodCard error, merchantSn:{}", foodCardOpenReqDTO.getMerchantSn(), e);
            return CuaCommonResultDTO.fail("更新海科饭卡支付失败");
        }    }

    /**
     * 配置饭卡支付交易参数
     *
     * @param tradeParamReqDTO 交易参数请求
     * @return 配置结果
     */
    @Override
    public CuaCommonResultDTO configFoodCardTradeParam(FoodCardConfigTradeParamReqDTO tradeParamReqDTO) {
        return  CuaCommonResultDTO.fail("接口已废弃");
    }

    @Override
    public CuaCommonResultDTO configLianlianFoodCardTradeParam(FoodCardConfigTradeParamReqDTO tradeParamReqDTO) {
        try {
            String merchantSn = tradeParamReqDTO.getMerchantSn();
            MerchantInfo merchantInfo = merchantBasicInfoBiz.getMerchantInfoBySn(merchantSn);
            if (Objects.isNull(merchantInfo)) {
                return CuaCommonResultDTO.fail("商户不存在");
            }
            Optional<MerchantProviderParams> lexinFoodCardParams =
                merchantTradeParamsBiz.getFoodCardParamsByProvider(merchantSn, ProviderEnum.PROVIDER_LEXIN.getValue());
            MerchantProviderParams foodCardParam;
            if (!lexinFoodCardParams.isPresent()) {
                foodCardParam = buildAndSaveNewLianLianFoodCardParams(merchantInfo, tradeParamReqDTO);
            } else {
                MerchantProviderParams existedHaikeFoodCardParam = lexinFoodCardParams.get();
                byte[] extra = existedHaikeFoodCardParam.getExtra();
                Map<String, String> extraMap = CommonUtil.bytes2Map(extra);
                extraMap.put(PROVIDER_MCH_ID, tradeParamReqDTO.getProviderMchId());
                extraMap.put(PROVIDER_MCH_SUB_ID, tradeParamReqDTO.getProviderMchSubId());
                extraMap.put(PROVIDER_AGREEMENT_NO, tradeParamReqDTO.getProviderAgreementNo());
                existedHaikeFoodCardParam.setExtra(CommonUtil.map2Bytes(extraMap));
                merchantProviderParamsMapper.updateByPrimaryKeySelective(existedHaikeFoodCardParam);
                foodCardParam = existedHaikeFoodCardParam;
            }
            String campsFoodDeliveryAppId = subBizParamsBiz.getCampsFoodDelivery();
            lexinProvider.openSmartTradeParams(foodCardParam, null, true, campsFoodDeliveryAppId);
            // 连连饭卡支付，单独配置三方参数
            Map<String,String> params = new HashMap<>();
            params.put(PROVIDER_MCH_ID,tradeParamReqDTO.getProviderMchId());
            params.put(PROVIDER_MCH_SUB_ID,tradeParamReqDTO.getProviderMchSubId());
            params.put(PROVIDER_AGREEMENT_NO, tradeParamReqDTO.getProviderAgreementNo());
            // 连连饭卡渠道，收钱吧商户号即为连连商户号
            tradeConfigService.updateLianLianTradeParams(tradeParamReqDTO.getProviderMchId(), params);
            SubBizConfig foodCardSubBizConfig = getFoodCardSubBizConfig();
            updateMiniStatusAndDefaultFeeRate(tradeParamReqDTO, merchantInfo.getId(), foodCardParam,
                campsFoodDeliveryAppId, foodCardSubBizConfig);
            configFeeRate(foodCardSubBizConfig, merchantSn);
            return CuaCommonResultDTO.success();
        } catch (ContractBizException e) {
            log.error("configFoodCardTradeParam error, merchantSn:{}", tradeParamReqDTO.getMerchantSn(), e);
            return CuaCommonResultDTO.fail(e.getMessage());
        } catch (Exception e) {
            log.error("configFoodCardTradeParam error, merchantSn:{}", tradeParamReqDTO.getMerchantSn(), e);
            return CuaCommonResultDTO.fail("配置饭卡支付交易参数失败");
        }
    }

    private MerchantProviderParams buildAndSaveNewLianLianFoodCardParams(MerchantInfo merchantInfo,
        FoodCardConfigTradeParamReqDTO tradeParamReqDTO) {
        // 创建商户在乐信通道payWay为0的交易参数
        produceAndInsertLexinParam(merchantInfo,tradeParamReqDTO);
        String merchantSn = merchantInfo.getSn();
        MerchantProviderParams merchantProviderParams = new MerchantProviderParams();
        merchantProviderParams.setId(UUID.randomUUID().toString());
        merchantProviderParams.setMerchant_sn(merchantSn);
        merchantProviderParams.setOut_merchant_sn(merchantSn);
        merchantProviderParams.setChannel_no(ChannelEnum.LEXIN_FOOD_CARD.getValue());
        merchantProviderParams.setParent_merchant_id(tradeParamReqDTO.getProviderMchId());
        merchantProviderParams.setProvider(ProviderEnum.PROVIDER_LEXIN.getValue());
        merchantProviderParams.setProvider_merchant_id(tradeParamReqDTO.getProviderMchId());
        merchantProviderParams.setPayway(PaywayEnum.FOOD_CARD.getValue());
        merchantProviderParams.setParams_config_status(ConfigStatusEnum.NOT_REQUIRE_CONFIG.getValue());
        merchantProviderParams.setPay_merchant_id(tradeParamReqDTO.getProviderMchSubId());
        merchantProviderParams.setContract_rule(DEFAULT_LEXIN_FOOD_CARD_RULE);
        merchantProviderParams.setRule_group_id(ChannelEnum.LEXIN_FOOD_CARD_NO.getValue());
        merchantProviderParams.setUpdate_status(UpdateStatusEnum.SUCCESS.getValue());
        merchantProviderParams.setCtime(System.currentTimeMillis());
        merchantProviderParams.setMtime(System.currentTimeMillis());
        merchantProviderParams.setDeleted(false);
        HashMap<String, String> extraMap = Maps.newHashMap();
        extraMap.put(PROVIDER_MCH_ID, tradeParamReqDTO.getProviderMchId());
        extraMap.put(PROVIDER_MCH_SUB_ID, tradeParamReqDTO.getProviderMchSubId());
        extraMap.put(PROVIDER_AGREEMENT_NO, tradeParamReqDTO.getProviderAgreementNo());
        merchantProviderParams.setExtra(CommonUtil.map2Bytes(extraMap));
        merchantProviderParamsMapper.insertSelective(merchantProviderParams);
        return merchantProviderParamsMapper.selectByPrimaryKey(merchantProviderParams.getId());
    }

    private void produceAndInsertLexinParam(MerchantInfo merchantInfo,
        FoodCardConfigTradeParamReqDTO tradeParamReqDTO) {
        String merchantSn = merchantInfo.getSn();
        MerchantProviderParams merchantProviderParams = new MerchantProviderParams().setId(UUID.randomUUID().toString())
            .setMerchant_sn(merchantSn).setOut_merchant_sn(merchantSn)
            .setChannel_no(ChannelEnum.LEXIN_FOOD_CARD_NO.getValue())
            .setParent_merchant_id(tradeParamReqDTO.getProviderMchId()).setPayway(PaywayEnum.ACQUIRER.getValue())
            .setProvider(ProviderEnum.PROVIDER_LEXIN.getValue())
            .setProvider_merchant_id(tradeParamReqDTO.getProviderMchId())
            .setParams_config_status(ConfigStatusEnum.NOT_REQUIRE_CONFIG.getValue())
            .setPay_merchant_id(tradeParamReqDTO.getProviderMchSubId()).setContract_rule(DEFAULT_LEXIN_FOOD_CARD_RULE)
            .setRule_group_id(ChannelEnum.LEXIN_FOOD_CARD_NO.getValue())
            .setUpdate_status(UpdateStatusEnum.SUCCESS.getValue()).setCtime(System.currentTimeMillis())
            .setMtime(System.currentTimeMillis()).setDeleted(false);
        merchantProviderParamsMapper.insertSelective(merchantProviderParams);
    }

    private static String getMerchantName(MerchantInfo merchantInfo) {
        return StringUtils.isBlank(merchantInfo.getName()) ? merchantInfo.getBusiness_name() : merchantInfo.getName();
    }

    private void configFeeRate(SubBizConfig foodCardSubBizConfig, String merchantSn) {
        try {
            if (Objects.nonNull(foodCardSubBizConfig)) {
                subBizParamsBiz.doComboByPayway(merchantSn, foodCardSubBizConfig, PaywayEnum.FOOD_CARD.getValue());
            }
        } catch (Exception e) {
            log.error("configFeeRate error, merchantSn:{}", merchantSn, e);
        }

    }

    /**
     * 更新状态mini_formal 和 mini_status,及默认的费率
     */
    private void updateMiniStatusAndDefaultFeeRate(FoodCardConfigTradeParamReqDTO tradeParamReqDTO, String merchantId, MerchantProviderParams foodCardParam, String campsFoodDeliveryAppId, SubBizConfig foodCardSubBizConfig) {
        Map merchantAppConfig = tradeConfigService.getMerchantAppConfigByMerchantIdAndPaywayAndApp(merchantId, foodCardParam.getPayway(), campsFoodDeliveryAppId);
        if (MapUtils.isEmpty(merchantAppConfig)) {
            log.error("configFoodCardTradeParam error, merchantSn:{}, 未成功配置饭卡支付交易参数", tradeParamReqDTO.getMerchantSn());
            throw new ContractBizException("配置饭卡支付交易参数失败");
        }
        Map updateAppMap = Maps.newHashMap();
        updateAppMap.put(DaoConstants.ID, merchantAppConfig.get(DaoConstants.ID));
        updateAppMap.put(MerchantAppConfig.MINI_STATUS, MerchantAppConfig.STATUS_OPENED);
        updateAppMap.put(MerchantAppConfig.MINI_FORMAL, MerchantAppConfig.STATUS_CLOSED);
        updateAppMap.put(MerchantAppConfig.MINI_FEE_RATE, getFoodCardFeeRate(foodCardSubBizConfig, PaywayEnum.FOOD_CARD.getValue()));
        tradeConfigService.updateMerchantAppConfig(updateAppMap);
    }


    private String getFoodCardFeeRate(SubBizConfig subBizConfig, Integer payWay) {
        if (MapUtils.isEmpty(subBizConfig.getFeeRate())) {
            return DEFAULT_FEE_RATE;
        }
        String feeRate = subBizConfig.getFeeRate().get(payWay);
        if (StringUtils.isBlank(feeRate)) {
            return DEFAULT_FEE_RATE;
        }
        return feeRate;
    }

    private SubBizConfig getFoodCardSubBizConfig() {
        Map appIdSubBizMap = applicationApolloConfig.getAppIdSubBiz();
        List<Map> values = JSONArray.parseArray(JSONObject.toJSONString(appIdSubBizMap.values()), Map.class);
        Map<String, Map> tradeNameConfig = values.stream().collect(Collectors.toMap(sub -> MapUtils.getString(sub, "mappingTradeAppId"), sub -> sub, (k1, k2) -> k1));
        Map map = tradeNameConfig.get(subBizParamsBiz.getCampsFoodDelivery());
        if (MapUtils.isEmpty(map)) {
            log.info("getFoodCardSubBizConfig empty,appName:{}", subBizParamsBiz.getCampsFoodDelivery());
            return null;
        }
        return JSONObject.parseObject(JSON.toJSONString(map), SubBizConfig.class);
    }

}
