package com.wosai.upay.job.refactor.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 支付业务申请流程节点状态枚举
 *
 * <AUTHOR>
@Getter
@AllArgsConstructor
public enum PayBizApplyFlowNodeStatusEnum {

    /**
     * 未处理
     */
    UNPROCESSED(0, "未处理"),

    /**
     * 进行中
     */
    IN_PROGRESS(1, "进行中"),

    /**
     * 成功
     */
    SUCCESS(2, "成功");

    /**
     * 状态码
     */
    private final Integer code;

    /**
     * 状态描述
     */
    private final String desc;

    /**
     * 根据状态码获取枚举
     *
     * @param code 状态码
     * @return 对应的枚举，如果不存在则返回null
     */
    public static PayBizApplyFlowNodeStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (PayBizApplyFlowNodeStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 判断是否为未处理状态
     *
     * @param code 状态码
     * @return true-未处理，false-非未处理
     */
    public static boolean isUnprocessed(Integer code) {
        return UNPROCESSED.getCode().equals(code);
    }

    /**
     * 判断是否为进行中状态
     *
     * @param code 状态码
     * @return true-进行中，false-非进行中
     */
    public static boolean isInProgress(Integer code) {
        return IN_PROGRESS.getCode().equals(code);
    }

    /**
     * 判断是否为成功状态
     *
     * @param code 状态码
     * @return true-成功，false-非成功
     */
    public static boolean isSuccess(Integer code) {
        return SUCCESS.getCode().equals(code);
    }

    /**
     * 判断是否为已完成状态（成功）
     *
     * @param code 状态码
     * @return true-已完成，false-未完成
     */
    public static boolean isFinished(Integer code) {
        return isSuccess(code);
    }

    /**
     * 判断是否为未完成状态（未处理或进行中）
     *
     * @param code 状态码
     * @return true-未完成，false-已完成
     */
    public static boolean isUnfinished(Integer code) {
        return !isFinished(code);
    }
}
