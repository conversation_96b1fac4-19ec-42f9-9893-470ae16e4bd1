package com.wosai.upay.job.refactor.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wosai.upay.job.enume.tonglianV2.PayBizApplyDetailStatusEnum;
import com.wosai.upay.job.enume.tonglianV2.PayBizApplyStatusEnum;
import com.wosai.upay.job.refactor.mapper.PayBizApplyDynamicMapper;
import com.wosai.upay.job.refactor.model.bo.PayBizApplyFlowInfoBO;
import com.wosai.upay.job.refactor.model.entity.PayBizApplyDO;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 支付业务开通申请单数据库访问层 {@link PayBizApplyDO}
 * 对PayBizApplyDynamicMapper层做出简单封装 {@link PayBizApplyDynamicMapper}
 *
 * <AUTHOR>
@Repository
public class PayBizApplyDAO extends AbstractBaseDAO<PayBizApplyDO, PayBizApplyDynamicMapper> {

    public PayBizApplyDAO(SqlSessionFactory sqlSessionFactory, PayBizApplyDynamicMapper entityMapper) {
        super(sqlSessionFactory, entityMapper);
    }

    /**
     * 根据商户号和支付业务编码查询记录
     *
     * @param merchantSn 商户号
     * @param devCode    支付业务编码
     * @return 支付业务申请记录
     */
    public Optional<PayBizApplyDO> selectByMerchantSnAndDevCode(String merchantSn, String devCode) {
        LambdaQueryWrapper<PayBizApplyDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PayBizApplyDO::getMerchantSn, merchantSn)
                .eq(PayBizApplyDO::getDevCode, devCode)
                .orderByDesc(PayBizApplyDO::getId)
                .last("limit 1");
        return selectOne(queryWrapper);
    }

    /**
     * 根据商户号查询所有申请记录
     *
     * @param merchantSn 商户号
     * @return 支付业务申请记录列表
     */
    public List<PayBizApplyDO> selectByMerchantSn(String merchantSn) {
        LambdaQueryWrapper<PayBizApplyDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PayBizApplyDO::getMerchantSn, merchantSn)
                .orderByDesc(PayBizApplyDO::getId);
        return entityMapper.selectList(queryWrapper);
    }

    /**
     * 根据状态和优先级查询待处理的申请记录
     *
     * @param status       主状态
     * @param detailStatus 详细状态
     * @param priority     优先级时间
     * @param limit        查询限制数量
     * @return 待处理的申请记录列表
     */
    public List<PayBizApplyDO> selectPendingApplies(Integer status, Integer detailStatus, LocalDateTime priority, Integer limit) {
        LambdaQueryWrapper<PayBizApplyDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PayBizApplyDO::getStatus, status)
                .eq(PayBizApplyDO::getDetailStatus, detailStatus)
                .le(PayBizApplyDO::getPriority, priority)
                .orderByAsc(PayBizApplyDO::getPriority)
                .last("limit " + limit);
        return entityMapper.selectList(queryWrapper);
    }

    /**
     * 根据状态查询申请记录
     *
     * @param status 主状态
     * @param limit  查询限制数量
     * @return 申请记录列表
     */
    public List<PayBizApplyDO> selectByStatus(Integer status, Integer limit) {
        LambdaQueryWrapper<PayBizApplyDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PayBizApplyDO::getStatus, status)
                .orderByAsc(PayBizApplyDO::getPriority)
                .last("limit " + limit);
        return entityMapper.selectList(queryWrapper);
    }

    /**
     * 更新申请记录
     *
     * @param payBizApplyDO 申请记录
     */
    public void update(PayBizApplyDO payBizApplyDO) {
        int affectedRows = entityMapper.updateById(payBizApplyDO);
        if (affectedRows < 1) {
            throw new ContractBizException("更新支付业务申请记录失败，请重试");
        }
    }

    /**
     * 保存申请记录
     *
     * @param payBizApplyDO 申请记录
     */
    public void save(PayBizApplyDO payBizApplyDO) {
        Integer affectedRows = insertOne(payBizApplyDO);
        if (affectedRows < 1) {
            throw new ContractBizException("保存支付业务申请记录失败，请重试");
        }
    }

    /**
     * 创建新的支付业务申请
     *
     * @param merchantSn 商户号
     * @param devCode    支付业务编码
     * @param formBody   提交报文
     * @param flowInfoBO 流程信息BO
     * @return 新创建的申请记录
     */
    public PayBizApplyDO createNewApply(String merchantSn, String devCode, String formBody, Long taskId, PayBizApplyFlowInfoBO flowInfoBO) {
        PayBizApplyDO newApply = PayBizApplyDO.createNewApply(merchantSn, devCode, formBody, taskId, flowInfoBO);
        save(newApply);
        return newApply;
    }

    /**
     * 更新调度优先级
     *
     * @param apply       申请记录
     * @param priority 优先级时间
     */
    public void updatePriority(PayBizApplyDO apply, LocalDateTime priority) {
        apply.updatePriority(priority);
        update(apply);
    }

    /**
     * 根据主状态枚举查询申请记录
     *
     * @param statusEnum 主状态枚举
     * @param limit      查询限制数量
     * @return 申请记录列表
     */
    public List<PayBizApplyDO> selectByStatusEnum(PayBizApplyStatusEnum statusEnum, Integer limit) {
        return selectByStatus(statusEnum.getCode(), limit);
    }

    /**
     * 根据详细状态枚举查询申请记录
     *
     * @param detailStatusEnum 详细状态枚举
     * @param limit            查询限制数量
     * @return 申请记录列表
     */
    public List<PayBizApplyDO> selectByDetailStatusEnum(PayBizApplyDetailStatusEnum detailStatusEnum, Integer limit) {
        LambdaQueryWrapper<PayBizApplyDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PayBizApplyDO::getDetailStatus, detailStatusEnum.getCode())
                .orderByAsc(PayBizApplyDO::getPriority)
                .last("limit " + limit);
        return entityMapper.selectList(queryWrapper);
    }

    /**
     * 查询所有开通中的申请记录
     *
     * @param limit 查询限制数量
     * @return 开通中的申请记录列表
     */
    public List<PayBizApplyDO> selectProcessingApplies(Integer limit) {
        return selectByStatusEnum(PayBizApplyStatusEnum.PROCESSING, limit);
    }

    /**
     * 查询所有审核中的申请记录
     *
     * @param limit 查询限制数量
     * @return 审核中的申请记录列表
     */
    public List<PayBizApplyDO> selectAuditingApplies(Integer limit) {
        LambdaQueryWrapper<PayBizApplyDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(PayBizApplyDO::getDetailStatus,
                        PayBizApplyDetailStatusEnum.CONTRACT_AUDITING.getCode(),
                        PayBizApplyDetailStatusEnum.COMPLIANCE_SUPPLEMENT_AUDITING.getCode())
                .orderByAsc(PayBizApplyDO::getPriority)
                .last("limit " + limit);
        return entityMapper.selectList(queryWrapper);
    }

    /**
     * 查询所有等待签约的申请记录
     *
     * @param limit 查询限制数量
     * @return 等待签约的申请记录列表
     */
    public List<PayBizApplyDO> selectWaitingSignApplies(String devCode, LocalDateTime queryTime, Integer limit) {
        LambdaQueryWrapper<PayBizApplyDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(PayBizApplyDO::getDetailStatus,
                        PayBizApplyDetailStatusEnum.WAITING_LEGAL_SIGN.getCode(),
                        PayBizApplyDetailStatusEnum.WAITING_SETTLEMENT_SIGN.getCode())
                .le(PayBizApplyDO::getDevCode, devCode)
                .ge(PayBizApplyDO::getPriority, queryTime)
                .orderByAsc(PayBizApplyDO::getPriority)
                .last("limit " + limit);
        return entityMapper.selectList(queryWrapper);
    }

    /**
     * 查询合规性审核中的申请记录
     *
     * @param devCode   业务编码
     * @param queryTime 查询时间
     * @param limit     查询限制数量
     * @return 合规性审核中的申请记录列表
     */
    public List<PayBizApplyDO> selectComplianceAuditingApplies(String devCode, LocalDateTime queryTime, Integer limit) {
        LambdaQueryWrapper<PayBizApplyDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PayBizApplyDO::getStatus, PayBizApplyStatusEnum.PROCESSING.getCode())
                .eq(PayBizApplyDO::getDetailStatus, PayBizApplyDetailStatusEnum.COMPLIANCE_SUPPLEMENT_AUDITING.getCode())
                .eq(PayBizApplyDO::getDevCode, devCode)
                .ge(PayBizApplyDO::getPriority, queryTime)
                .orderByAsc(PayBizApplyDO::getPriority)
                .last("limit " + limit);
        return entityMapper.selectList(queryWrapper);
    }

    /**
     * 查询待合规性补录的申请记录
     *
     * @param devCode   业务编码
     * @param queryTime 查询时间
     * @param limit     查询限制数量
     * @return 待合规性补录的申请记录列表
     */
    public List<PayBizApplyDO> selectComplianceRepairApplies(String devCode, LocalDateTime queryTime, Integer limit) {
        LambdaQueryWrapper<PayBizApplyDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PayBizApplyDO::getStatus, PayBizApplyStatusEnum.PROCESSING.getCode())
                .eq(PayBizApplyDO::getDetailStatus, PayBizApplyDetailStatusEnum.WAITING_SUPPLEMENT.getCode())
                .eq(PayBizApplyDO::getDevCode, devCode)
                .ge(PayBizApplyDO::getPriority, queryTime)
                .orderByAsc(PayBizApplyDO::getPriority)
                .last("limit " + limit);
        return entityMapper.selectList(queryWrapper);
    }

    /**
     * 设置申请为启用失败
     *
     * @param payBizApply 申请记录
     * @param result      失败原因
     */
    public void updateToEnabledFailed(PayBizApplyDO payBizApply, String result) {
        payBizApply.updateStatusToEnabledFailed(result);
        update(payBizApply);
    }

    /**
     * 设置申请为启用中
     *
     * @param apply   申请记录
     * @param applyId 收单机构切换申请ID
     */
    public void updateToEnabling(PayBizApplyDO apply, String applyId) {
        apply.updateStatusToEnabling(applyId);
        update(apply);
    }

    public void updateToContractFailed(PayBizApplyDO apply, String result) {
        apply.updateStatusToContractFailed(result);
        update(apply);
    }

    public void updateToExpired(PayBizApplyDO apply) {
        apply.updateStatusToExpired();
        update(apply);
    }

    public void updateToSignFailed(PayBizApplyDO apply) {
        apply.updateToSignFailed();
        update(apply);
    }

    public void updateToComplianceFailed(PayBizApplyDO apply, String result, String handledetail) {
        apply.updateToComplianceFailed(result, handledetail);
        update(apply);
    }

    /**
     * 标记流程节点为完成并更新状态
     * 同时将下一个节点置为进行中
     *
     * @param apply            申请记录
     * @param statusEnum       主状态枚举
     * @param detailStatusEnum 详细状态枚举
     */
    public void markFlowNodeFinishedAndUpdateStatus(PayBizApplyDO apply, PayBizApplyStatusEnum statusEnum,
                                                    PayBizApplyDetailStatusEnum detailStatusEnum) {
        // 标记当前节点为完成并启动下一个节点
        PayBizApplyFlowInfoBO flowInfo = apply.getFlowInfoBO();
        boolean success = flowInfo.completeCurrentAndStartNext(LocalDateTime.now());
        if (success) {
            apply.setFlowInfoBO(flowInfo);
        }

        // 更新状态
        apply.updateStatus(statusEnum.getCode(), detailStatusEnum.getCode(), detailStatusEnum.getDesc());

        update(apply);
    }

    /**
     * 完成当前阶段并进入下一阶段 & 更新详细状态
     *
     * @param apply                   申请记录
     */
    public void completeCurrentStageAndMoveToNext(PayBizApplyDO apply) {
        // 标记当前节点为完成
        apply.completeCurrentStageAndMoveToNext();
        update(apply);
    }

    /**
     * 执行状态回退并更新表单数据（充血模型方法）
     *
     * @param apply    申请单对象
     * @param formBody 新的表单数据，可以为空
     * @return true-回退并更新成功，false-回退失败
     */
    public boolean rollbackStatusAndUpdateForm(PayBizApplyDO apply, String formBody, Long taskId) {
        // 执行状态回退
        boolean rollbackSuccess = apply.rollbackStatus();
        if (!rollbackSuccess) {
            return false;
        }

        // 更新表单数据
        if (!StringUtils.isEmpty(formBody)) {
            apply.setFormBody(formBody);
        }
        if (Objects.nonNull(taskId)) {
            apply.updateTaskId(taskId);
        }

        // 保存更新到数据库
        update(apply);

        return true;
    }

    /**
     * 将申请单置为开通成功状态
     * 同时将所有待处理/进行中的节点置为成功
     *
     * @param apply 申请记录
     */
    public void markApplyAsSuccess(PayBizApplyDO apply) {
        apply.markAsSuccess();
        update(apply);
    }

    public void saveLegalSignUrl(PayBizApplyDO apply, String url) {
        apply.saveLegalSignUrl(url);
        update(apply);
    }

    /**
     * 更新签约链接
     *
     * @param apply 申请单
     * @param url   签约链接
     */
    public void updateSignUrl(PayBizApplyDO apply, String url) {
        apply.updateSignUrl(url);
        update(apply);
    }

    public void saveSettlementSignUrl(PayBizApplyDO apply, String url) {
        apply.saveSettlementSignUrl(url);
        update(apply);
    }
}
