package com.wosai.upay.job.refactor.model.bo;

import com.alibaba.fastjson.JSON;
import com.wosai.upay.job.enume.tonglianV2.PayBizApplyDetailStatusEnum;
import com.wosai.upay.job.enume.tonglianV2.PayBizApplyStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 支付业务申请操作日志详情BO
 *
 * <AUTHOR>
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PayBizApplyLogDetailBO {

    /**
     * 状态码
     */
    private Integer status;

    /**
     * 详细状态码
     */
    private Integer detailStatus;

    /**
     * 展示文案
     */
    private String displayText;

    /**
     * 操作描述
     */
    private String description;

    /**
     * 操作时间
     */
    private LocalDateTime operateTime;

    /**
     * 转换为JSON字符串
     *
     * @return JSON字符串
     */
    public String toJsonString() {
        return JSON.toJSONString(this);
    }

    /**
     * 从JSON字符串创建对象
     *
     * @param jsonString JSON字符串
     * @return PayBizApplyLogDetailBO对象
     */
    public static PayBizApplyLogDetailBO fromJsonString(String jsonString) {
        try {
            return JSON.parseObject(jsonString, PayBizApplyLogDetailBO.class);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 创建日志详情
     *
     * @param status 状态码
     * @param detailStatus 详细状态码
     * @return 日志详情BO
     */
    public static PayBizApplyLogDetailBO create(Integer status, Integer detailStatus) {
        PayBizApplyDetailStatusEnum detailStatusEnum = PayBizApplyDetailStatusEnum.getByCode(detailStatus);
        return new PayBizApplyLogDetailBO(status, detailStatus, detailStatusEnum.getDisplayText(), detailStatusEnum.getDescription(), LocalDateTime.now());
    }

    public static PayBizApplyLogDetailBO create(PayBizApplyStatusEnum statusEnum, PayBizApplyDetailStatusEnum detailStatusEnum) {
        return new PayBizApplyLogDetailBO(statusEnum.getCode(), detailStatusEnum.getCode(), detailStatusEnum.getDisplayText(), detailStatusEnum.getDescription(), LocalDateTime.now());
    }

    public static PayBizApplyLogDetailBO create(PayBizApplyStatusEnum statusEnum, PayBizApplyDetailStatusEnum detailStatusEnum, String description) {
        return new PayBizApplyLogDetailBO(statusEnum.getCode(), detailStatusEnum.getCode(), detailStatusEnum.getDisplayText(), description, LocalDateTime.now());
    }




}
