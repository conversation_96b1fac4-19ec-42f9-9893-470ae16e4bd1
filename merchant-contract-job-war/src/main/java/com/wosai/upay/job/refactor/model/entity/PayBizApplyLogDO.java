package com.wosai.upay.job.refactor.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;

/**
 * 支付业务操作流水表实体对象
 *
 * <AUTHOR>
@TableName("pay_biz_apply_log")
@Data
@Slf4j
public class PayBizApplyLogDO {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 业务主键值
     */
    @TableField(value = "biz_id")
    private String bizId;

    /**
     * 操作类型：INSERT / UPDATE / DELETE / SUBMIT / AUDIT 等
     */
    @TableField(value = "operator_type")
    private String operatorType;

    /**
     * 操作者名称/账号
     */
    @TableField(value = "operator")
    private String operator;

    /**
     * 操作人id
     */
    @TableField(value = "operator_id")
    private String operatorId;

    /**
     * 操作平台：CRM、SYSTEM、SPA 等
     */
    @TableField(value = "platform")
    private String platform;

    /**
     * 变更相关信息（JSON 字符串）
     */
    @TableField(value = "detail")
    private String detail;

    /**
     * 任意扩展字符串
     */
    @TableField(value = "extra")
    private String extra;

    /**
     * 流水时间
     */
    @TableField(value = "ctime")
    private LocalDateTime ctime;

    // ==================== 充血模型：业务方法 ====================

    /**
     * 创建新的操作流水记录
     *
     * @param bizId 业务主键值
     * @param operatorType 操作类型
     * @param operator 操作者名称
     * @param operatorId 操作人ID
     * @param platform 操作平台
     * @param detail 变更详情
     * @return 新的流水记录实例
     */
    public static PayBizApplyLogDO createNewLog(String bizId, String operatorType, String operator, 
                                               String operatorId, String platform, String detail) {
        PayBizApplyLogDO log = new PayBizApplyLogDO();
        log.bizId = bizId;
        log.operatorType = operatorType;
        log.operator = operator;
        log.operatorId = operatorId;
        log.platform = platform;
        log.detail = detail;
        log.ctime = LocalDateTime.now();
        return log;
    }

    /**
     * 创建新的操作流水记录（带扩展信息）
     *
     * @param bizId 业务主键值
     * @param operatorType 操作类型
     * @param operator 操作者名称
     * @param operatorId 操作人ID
     * @param platform 操作平台
     * @param detail 变更详情
     * @param extra 扩展信息
     * @return 新的流水记录实例
     */
    public static PayBizApplyLogDO createNewLogWithExtra(String bizId, String operatorType, String operator, 
                                                        String operatorId, String platform, String detail, String extra) {
        PayBizApplyLogDO log = createNewLog(bizId, operatorType, operator, operatorId, platform, detail);
        log.extra = extra;
        return log;
    }

    /**
     * 创建插入操作流水
     *
     * @param bizId 业务主键值
     * @param operator 操作者名称
     * @param operatorId 操作人ID
     * @param platform 操作平台
     * @param detail 变更详情
     * @return 插入操作流水记录
     */
    public static PayBizApplyLogDO createInsertLog(String bizId, String operator, String operatorId, 
                                                  String platform, String detail) {
        return createNewLog(bizId, "INSERT", operator, operatorId, platform, detail);
    }

    /**
     * 创建更新操作流水
     *
     * @param bizId 业务主键值
     * @param operator 操作者名称
     * @param operatorId 操作人ID
     * @param platform 操作平台
     * @param detail 变更详情
     * @return 更新操作流水记录
     */
    public static PayBizApplyLogDO createUpdateLog(String bizId, String operator, String operatorId, 
                                                  String platform, String detail) {
        return createNewLog(bizId, "UPDATE", operator, operatorId, platform, detail);
    }

    /**
     * 创建提交操作流水
     *
     * @param bizId 业务主键值
     * @param operator 操作者名称
     * @param operatorId 操作人ID
     * @param platform 操作平台
     * @param detail 变更详情
     * @return 提交操作流水记录
     */
    public static PayBizApplyLogDO createSubmitLog(String bizId, String operator, String operatorId, 
                                                  String platform, String detail) {
        return createNewLog(bizId, "SUBMIT", operator, operatorId, platform, detail);
    }

    /**
     * 创建审核操作流水
     *
     * @param bizId 业务主键值
     * @param operator 操作者名称
     * @param operatorId 操作人ID
     * @param platform 操作平台
     * @param detail 变更详情
     * @return 审核操作流水记录
     */
    public static PayBizApplyLogDO createAuditLog(String bizId, String operator, String operatorId, 
                                                 String platform, String detail) {
        return createNewLog(bizId, "AUDIT", operator, operatorId, platform, detail);
    }

    /**
     * 判断是否为插入操作
     *
     * @return true-插入操作，false-非插入操作
     */
    public boolean isInsertOperation() {
        return "INSERT".equals(this.operatorType);
    }

    /**
     * 判断是否为更新操作
     *
     * @return true-更新操作，false-非更新操作
     */
    public boolean isUpdateOperation() {
        return "UPDATE".equals(this.operatorType);
    }

    /**
     * 判断是否为提交操作
     *
     * @return true-提交操作，false-非提交操作
     */
    public boolean isSubmitOperation() {
        return "SUBMIT".equals(this.operatorType);
    }

    /**
     * 判断是否为审核操作
     *
     * @return true-审核操作，false-非审核操作
     */
    public boolean isAuditOperation() {
        return "AUDIT".equals(this.operatorType);
    }
}
